---
import MainLayout from '../../layouts/MainLayout.astro';

export const prerender = false;
---

<MainLayout title="Payment Status - Sreekar Publishers">
  <div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-md mx-auto px-4">
      <div id="status-container" class="bg-white rounded-xl shadow-lg p-6">
        <!-- Loading State -->
        <div id="loading-state" class="text-center">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-[#5466F7] mx-auto mb-4"></div>
          <h2 class="text-xl font-semibold text-gray-800 mb-2">Checking Payment Status</h2>
          <p class="text-gray-600">Please wait while we verify your payment...</p>
        </div>

        <!-- Success State -->
        <div id="success-state" class="text-center hidden">
          <div class="bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
            <span class="material-icons-round text-green-600 text-3xl">check_circle</span>
          </div>
          <h2 class="text-xl font-semibold text-gray-800 mb-2">Payment Successful!</h2>
          <p class="text-gray-600 mb-4">Your order has been placed successfully.</p>
          <div class="bg-gray-50 rounded-lg p-4 mb-4">
            <p class="text-sm text-gray-600">Order ID: <span id="order-id" class="font-medium text-gray-800"></span></p>
            <p class="text-sm text-gray-600">Amount: <span id="payment-amount" class="font-medium text-gray-800"></span></p>
          </div>
          <button 
            onclick="window.location.href='/orders'"
            class="w-full bg-[#5466F7] text-white font-medium py-3 px-6 rounded-lg hover:bg-blue-600 transition-colors"
          >
            View My Orders
          </button>
        </div>

        <!-- Failed State -->
        <div id="failed-state" class="text-center hidden">
          <div class="bg-red-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
            <span class="material-icons-round text-red-600 text-3xl">error</span>
          </div>
          <h2 class="text-xl font-semibold text-gray-800 mb-2">Payment Failed</h2>
          <p class="text-gray-600 mb-4" id="error-message">Your payment could not be processed. Please try again.</p>
          <div class="space-y-3">
            <button 
              id="retry-payment"
              class="w-full bg-[#5466F7] text-white font-medium py-3 px-6 rounded-lg hover:bg-blue-600 transition-colors"
            >
              Retry Payment
            </button>
            <button 
              onclick="window.location.href='/cart'"
              class="w-full bg-gray-200 text-gray-800 font-medium py-3 px-6 rounded-lg hover:bg-gray-300 transition-colors"
            >
              Back to Cart
            </button>
          </div>
        </div>

        <!-- Pending State -->
        <div id="pending-state" class="text-center hidden">
          <div class="bg-yellow-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
            <span class="material-icons-round text-yellow-600 text-3xl">schedule</span>
          </div>
          <h2 class="text-xl font-semibold text-gray-800 mb-2">Payment Pending</h2>
          <p class="text-gray-600 mb-4">Your payment is being processed. This may take a few minutes.</p>
          <div class="space-y-3">
            <button 
              id="check-status"
              class="w-full bg-[#5466F7] text-white font-medium py-3 px-6 rounded-lg hover:bg-blue-600 transition-colors"
            >
              Check Status Again
            </button>
            <button 
              onclick="window.location.href='/orders'"
              class="w-full bg-gray-200 text-gray-800 font-medium py-3 px-6 rounded-lg hover:bg-gray-300 transition-colors"
            >
              View Orders
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    // Get transaction ID from URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const transactionId = urlParams.get('transactionId');

    // Elements
    const loadingState = document.getElementById('loading-state');
    const successState = document.getElementById('success-state');
    const failedState = document.getElementById('failed-state');
    const pendingState = document.getElementById('pending-state');

    // Show specific state
    function showState(state) {
      // Hide all states
      loadingState.classList.add('hidden');
      successState.classList.add('hidden');
      failedState.classList.add('hidden');
      pendingState.classList.add('hidden');

      // Show requested state
      state.classList.remove('hidden');
    }

    // Format currency
    function formatCurrency(amount) {
      return new Intl.NumberFormat('en-IN', {
        style: 'currency',
        currency: 'INR',
        minimumFractionDigits: 2
      }).format(amount);
    }

    // Check payment status
    async function checkPaymentStatus() {
      if (!transactionId) {
        showError('Invalid payment request. Transaction ID is missing.');
        return;
      }

      try {
        const response = await window.ApiClient.getPaymentStatus(transactionId);
        
        if (!response.success) {
          showError(response.message || 'Failed to check payment status');
          return;
        }

        const transaction = response.transaction;
        
        switch (transaction.status) {
          case 'success':
            showSuccess(transaction);
            break;
          case 'failed':
            showError('Payment failed. Please try again.');
            break;
          case 'pending':
          case 'initiated':
            showPending();
            break;
          default:
            showError('Unknown payment status. Please contact support.');
        }

      } catch (error) {
        console.error('Error checking payment status:', error);
        showError('Failed to check payment status. Please try again.');
      }
    }

    // Show success state
    function showSuccess(transaction) {
      document.getElementById('order-id').textContent = transaction.order_id;
      document.getElementById('payment-amount').textContent = formatCurrency(transaction.amount);
      showState(successState);
    }

    // Show error state
    function showError(message) {
      document.getElementById('error-message').textContent = message;
      showState(failedState);
    }

    // Show pending state
    function showPending() {
      showState(pendingState);
    }

    // Event listeners
    document.getElementById('retry-payment')?.addEventListener('click', () => {
      window.location.href = '/checkout';
    });

    document.getElementById('check-status')?.addEventListener('click', () => {
      showState(loadingState);
      setTimeout(checkPaymentStatus, 1000); // Brief delay for better UX
    });

    // Auto-check status on page load
    document.addEventListener('DOMContentLoaded', () => {
      // Wait a moment for any pending webhook updates
      setTimeout(checkPaymentStatus, 2000);
    });

    // Notification system
    function showNotification(message, type = 'info') {
      const notificationContainer = document.createElement('div');
      notificationContainer.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm ${
        type === 'success' ? 'bg-green-100 border border-green-300 text-green-800' :
        type === 'error' ? 'bg-red-100 border border-red-300 text-red-800' :
        'bg-blue-100 border border-blue-300 text-blue-800'
      }`;
      
      notificationContainer.innerHTML = `
        <div class="flex items-start">
          <span class="material-icons-round mr-2 text-sm">
            ${type === 'success' ? 'check_circle' : type === 'error' ? 'error' : 'info'}
          </span>
          <p class="text-sm font-medium">${message}</p>
        </div>
      `;
      
      document.body.appendChild(notificationContainer);
      
      // Auto-remove after 5 seconds
      setTimeout(() => {
        notificationContainer.remove();
      }, 5000);
    }

    // Add API client method if not already present
    if (window.ApiClient && !window.ApiClient.getPaymentStatus) {
      window.ApiClient.getPaymentStatus = async function(transactionId) {
        try {
          const response = await this.request(`/api/payments/status?transactionId=${encodeURIComponent(transactionId)}`, {
            method: 'GET'
          });

          if (!response.ok) {
            throw new Error(`Failed to get payment status: ${response.statusText}`);
          }

          return await response.json();
        } catch (error) {
          console.error('Error getting payment status:', error);
          return {
            success: false,
            error: error.message
          };
        }
      };
    }
  </script>
</MainLayout> 